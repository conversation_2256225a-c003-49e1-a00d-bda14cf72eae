from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.contrib import messages
from apps.students.models import Student
from apps.staffs.models import Staff
from apps.attendance.models import Attendance
from apps.exams.models import Exam, Mark
from apps.documents.models import Document


@login_required
def teacher_dashboard(request):
    """Main teacher dashboard"""
    try:
        # Get the staff object for the logged-in user
        staff = Staff.objects.get(user=request.user)

        # Get teacher's assigned classes/students (you may need to adjust this based on your model structure)
        teacher_students = Student.objects.filter(current_status='active')[:10]  # Limit for dashboard
        recent_exams = Exam.objects.all()[:5]

        context = {
            'staff': staff,
            'teacher_students': teacher_students,
            'recent_exams': recent_exams,
            'total_students': teacher_students.count(),
        }
        return render(request, 'TeacherDashboard/dashboard.html', context)
    except Staff.DoesNotExist:
        messages.error(request, 'Staff profile not found. Please contact administrator.')
        return render(request, 'TeacherDashboard/dashboard.html', {})


@login_required
def teacher_students_list(request):
    """Teacher-specific student list - READ ONLY"""
    try:
        staff = Staff.objects.get(user=request.user)
        # Get students assigned to this teacher (read-only access)
        # Teachers can only VIEW students, not create/edit/delete
        students = Student.objects.filter(current_status='active').order_by('fullname')

        # Add filtering by class if needed
        class_filter = request.GET.get('class')
        section_filter = request.GET.get('section')

        if class_filter:
            students = students.filter(current_class_id=class_filter)
        if section_filter:
            students = students.filter(section=section_filter)

        context = {
            'students': students,
            'staff': staff,
            'is_teacher_view': True,  # Flag to indicate this is teacher view
            'total_students': students.count(),
        }
        return render(request, 'TeacherDashboard/students_list.html', context)
    except Staff.DoesNotExist:
        messages.error(request, 'Staff profile not found.')
        return render(request, 'TeacherDashboard/students_list.html', {'is_teacher_view': True})


@login_required
def teacher_student_detail(request, pk):
    """Teacher-specific student detail view - READ ONLY"""
    student = get_object_or_404(Student, pk=pk)
    try:
        staff = Staff.objects.get(user=request.user)

        # Get student's attendance summary (last 30 days)
        from datetime import datetime, timedelta
        thirty_days_ago = datetime.now().date() - timedelta(days=30)
        total_attendance = Attendance.objects.filter(
            student=student,
            date__gte=thirty_days_ago
        ).count()
        present_count = Attendance.objects.filter(
            student=student,
            date__gte=thirty_days_ago,
            status='present'
        ).count()

        attendance_percentage = (present_count / total_attendance * 100) if total_attendance > 0 else 0

        # Get recent marks
        recent_marks = Mark.objects.filter(student=student).order_by('-created_at')[:5]

        context = {
            'student': student,
            'staff': staff,
            'is_teacher_view': True,  # Flag to indicate this is teacher view
            'attendance_percentage': round(attendance_percentage, 1),
            'total_attendance_days': total_attendance,
            'present_days': present_count,
            'recent_marks': recent_marks,
        }
        return render(request, 'TeacherDashboard/student_detail.html', context)
    except Staff.DoesNotExist:
        messages.error(request, 'Staff profile not found.')
        return render(request, 'TeacherDashboard/student_detail.html', {
            'student': student,
            'is_teacher_view': True
        })


@login_required
def teacher_attendance_list(request):
    """Teacher-specific attendance management"""
    try:
        staff = Staff.objects.get(user=request.user)
        # Get attendance records for teacher's classes
        attendance_records = Attendance.objects.all()[:20]  # Adjust query as needed

        context = {
            'attendance_records': attendance_records,
            'staff': staff,
        }
        return render(request, 'TeacherDashboard/attendance_list.html', context)
    except Staff.DoesNotExist:
        messages.error(request, 'Staff profile not found.')
        return render(request, 'TeacherDashboard/attendance_list.html', {})


@login_required
def teacher_attendance_mark(request):
    """Teacher attendance marking interface"""
    try:
        staff = Staff.objects.get(user=request.user)
        students = Student.objects.filter(current_status='active')

        context = {
            'students': students,
            'staff': staff,
        }
        return render(request, 'TeacherDashboard/attendance_mark.html', context)
    except Staff.DoesNotExist:
        messages.error(request, 'Staff profile not found.')
        return render(request, 'TeacherDashboard/attendance_mark.html', {})


@login_required
def teacher_exams_list(request):
    """Teacher-specific exams list"""
    try:
        staff = Staff.objects.get(user=request.user)
        exams = Exam.objects.all()

        context = {
            'exams': exams,
            'staff': staff,
        }
        return render(request, 'TeacherDashboard/exams_list.html', context)
    except Staff.DoesNotExist:
        messages.error(request, 'Staff profile not found.')
        return render(request, 'TeacherDashboard/exams_list.html', {})


@login_required
def teacher_marks_entry(request):
    """Teacher marks entry interface"""
    try:
        staff = Staff.objects.get(user=request.user)
        exams = Exam.objects.all()
        students = Student.objects.filter(current_status='active')

        context = {
            'exams': exams,
            'students': students,
            'staff': staff,
        }
        return render(request, 'TeacherDashboard/marks_entry.html', context)
    except Staff.DoesNotExist:
        messages.error(request, 'Staff profile not found.')
        return render(request, 'TeacherDashboard/marks_entry.html', {})


@login_required
def teacher_documents_list(request):
    """Teacher-specific documents"""
    try:
        staff = Staff.objects.get(user=request.user)
        documents = Document.objects.all()[:20]  # Adjust query as needed

        context = {
            'documents': documents,
            'staff': staff,
        }
        return render(request, 'TeacherDashboard/documents_list.html', context)
    except Staff.DoesNotExist:
        messages.error(request, 'Staff profile not found.')
        return render(request, 'TeacherDashboard/documents_list.html', {})


@login_required
def teacher_profile(request):
    """Teacher profile management"""
    try:
        staff = Staff.objects.get(user=request.user)

        context = {
            'staff': staff,
        }
        return render(request, 'TeacherDashboard/profile.html', context)
    except Staff.DoesNotExist:
        messages.error(request, 'Staff profile not found.')
        return render(request, 'TeacherDashboard/profile.html', {})


@login_required
def teacher_admissions(request):
    """Teacher view for admissions - Read Only"""
    try:
        staff = Staff.objects.get(user=request.user)

        # Teachers can view admission information but cannot create new admissions
        # Show recent admissions and admission statistics
        recent_students = Student.objects.filter(current_status='active').order_by('-date_of_admission')[:20]

        # Get admission statistics
        from datetime import datetime, timedelta
        current_year = datetime.now().year
        this_year_admissions = Student.objects.filter(date_of_admission__year=current_year).count()
        last_month = datetime.now() - timedelta(days=30)
        recent_admissions = Student.objects.filter(date_of_admission__gte=last_month).count()

        context = {
            'staff': staff,
            'recent_students': recent_students,
            'this_year_admissions': this_year_admissions,
            'recent_admissions': recent_admissions,
            'is_teacher_view': True,
        }
        return render(request, 'TeacherDashboard/admissions.html', context)
    except Staff.DoesNotExist:
        messages.error(request, 'Staff profile not found.')
        return render(request, 'TeacherDashboard/admissions.html', {'is_teacher_view': True})


@login_required
def teacher_udise_form(request):
    """Teacher view for UDISE+ forms - Read Only"""
    try:
        staff = Staff.objects.get(user=request.user)

        # Teachers can view UDISE+ information but cannot create/edit forms
        # Show students with UDISE+ information
        students_with_udise = Student.objects.filter(current_status='active')

        # Get UDISE+ statistics
        total_students = students_with_udise.count()

        context = {
            'staff': staff,
            'students': students_with_udise,
            'total_students': total_students,
            'is_teacher_view': True,
        }
        return render(request, 'TeacherDashboard/udise_form.html', context)
    except Staff.DoesNotExist:
        messages.error(request, 'Staff profile not found.')
        return render(request, 'TeacherDashboard/udise_form.html', {'is_teacher_view': True})
