{% extends 'TeacherDashboard/base.html' %}
{% load humanize %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'teacher_dashboard' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Dashboard
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="{% url 'teacher_students_list' %}" class="text-decoration-none">
          <i class="fas fa-user-graduate"></i> Students
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-user"></i> {{ student.fullname }}
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-user{% endblock title-icon %}
{% block title %}{{ student.fullname }}{% endblock title %}
{% block subtitle %}Student Details{% endblock subtitle %}

{% block content %}
<div class="container-fluid">
  <!-- Teacher Access Notice -->
  <div class="row mb-3">
    <div class="col-12">
      <div class="alert alert-info border-left-info">
        <div class="d-flex align-items-center">
          <i class="fas fa-eye fa-2x text-info me-3"></i>
          <div>
            <h6 class="mb-1"><strong>Teacher View - Read Only Access</strong></h6>
            <p class="mb-0">You are viewing student information as a teacher. You cannot edit student details, but you can mark attendance and enter marks.</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-8">
      <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-user me-2"></i>Student Information</h5>
            <span class="badge bg-light text-dark">
              <i class="fas fa-lock me-1"></i>Read Only
            </span>
          </div>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label fw-bold">Full Name:</label>
                <p class="form-control-plaintext">{{ student.fullname }}</p>
              </div>
              <div class="mb-3">
                <label class="form-label fw-bold">Registration Number:</label>
                <p class="form-control-plaintext">{{ student.registration_number|default:"N/A" }}</p>
              </div>
              <div class="mb-3">
                <label class="form-label fw-bold">Gender:</label>
                <p class="form-control-plaintext">{{ student.gender|title }}</p>
              </div>
              <div class="mb-3">
                <label class="form-label fw-bold">Date of Birth:</label>
                <p class="form-control-plaintext">{{ student.date_of_birth|date:"d M Y" }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label fw-bold">Class:</label>
                <p class="form-control-plaintext">{{ student.current_class|default:"N/A" }}</p>
              </div>
              <div class="mb-3">
                <label class="form-label fw-bold">Section:</label>
                <p class="form-control-plaintext">{{ student.section|default:"N/A" }}</p>
              </div>
              <div class="mb-3">
                <label class="form-label fw-bold">Mobile Number:</label>
                <p class="form-control-plaintext">{{ student.mobile_number|default:"N/A" }}</p>
              </div>
              <div class="mb-3">
                <label class="form-label fw-bold">Date of Admission:</label>
                <p class="form-control-plaintext">{{ student.date_of_admission|date:"d M Y" }}</p>
              </div>
            </div>
          </div>
          
          {% if student.address %}
          <div class="mb-3">
            <label class="form-label fw-bold">Address:</label>
            <p class="form-control-plaintext">{{ student.address }}</p>
          </div>
          {% endif %}
          
          <div class="mb-3">
            <label class="form-label fw-bold">Status:</label>
            <span class="badge {% if student.current_status == 'active' %}bg-success{% else %}bg-danger{% endif %}">
              {{ student.current_status|title }}
            </span>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-4">
      <div class="card shadow-sm">
        <div class="card-header bg-info text-white">
          <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Quick Stats</h5>
        </div>
        <div class="card-body">
          <div class="mb-3">
            <label class="form-label fw-bold">Total Attendance:</label>
            <div class="progress">
              <div class="progress-bar bg-success" role="progressbar" style="width: 85%" aria-valuenow="85" aria-valuemin="0" aria-valuemax="100">85%</div>
            </div>
          </div>
          
          <div class="mb-3">
            <label class="form-label fw-bold">Average Grade:</label>
            <span class="badge bg-success fs-6">B+</span>
          </div>
          
          <div class="mb-3">
            <label class="form-label fw-bold">Last Exam Score:</label>
            <span class="badge bg-info fs-6">78/100</span>
          </div>
        </div>
      </div>
      
      <div class="card shadow-sm mt-3">
        <div class="card-header bg-info text-white">
          <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Academic Performance</h5>
        </div>
        <div class="card-body">
          <div class="mb-3">
            <label class="form-label fw-bold">Attendance (Last 30 days):</label>
            <div class="progress mb-2">
              <div class="progress-bar {% if attendance_percentage >= 75 %}bg-success{% elif attendance_percentage >= 50 %}bg-warning{% else %}bg-danger{% endif %}"
                   role="progressbar"
                   style="width: {{ attendance_percentage }}%"
                   aria-valuenow="{{ attendance_percentage }}"
                   aria-valuemin="0"
                   aria-valuemax="100">
                {{ attendance_percentage }}%
              </div>
            </div>
            <small class="text-muted">{{ present_days }}/{{ total_attendance_days }} days present</small>
          </div>

          {% if recent_marks %}
          <div class="mb-3">
            <label class="form-label fw-bold">Recent Exam Results:</label>
            {% for mark in recent_marks %}
            <div class="d-flex justify-content-between align-items-center border-bottom py-2">
              <span>{{ mark.exam.name }} - {{ mark.subject.name }}</span>
              <span class="badge {% if mark.marks >= 80 %}bg-success{% elif mark.marks >= 60 %}bg-info{% elif mark.marks >= 40 %}bg-warning{% else %}bg-danger{% endif %}">
                {{ mark.marks }}/{{ mark.total_marks }}
              </span>
            </div>
            {% endfor %}
          </div>
          {% endif %}
        </div>
      </div>

      <div class="card shadow-sm mt-3">
        <div class="card-header bg-warning text-dark">
          <h5 class="mb-0"><i class="fas fa-tools me-2"></i>Teacher Actions</h5>
        </div>
        <div class="card-body">
          <div class="alert alert-info mb-3">
            <i class="fas fa-info-circle me-2"></i>
            <small>As a teacher, you can view student information and perform teaching-related tasks.</small>
          </div>
          <div class="d-grid gap-2">
            <a href="{% url 'teacher_attendance_mark' %}" class="btn btn-outline-primary">
              <i class="fas fa-calendar-check"></i> Mark Attendance
            </a>
            <a href="{% url 'teacher_marks_entry' %}" class="btn btn-outline-success">
              <i class="fas fa-clipboard-check"></i> Enter Marks
            </a>
            <a href="{% url 'teacher_students_list' %}" class="btn btn-outline-secondary">
              <i class="fas fa-arrow-left"></i> Back to Students List
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}
