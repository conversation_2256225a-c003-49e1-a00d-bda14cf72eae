from django.contrib.auth.decorators import user_passes_test
from django.core.exceptions import PermissionDenied
from django.shortcuts import redirect
from django.contrib import messages
from apps.staffs.models import Staff
from functools import wraps

def teacher_required(view_func):
    """
    Decorator that checks if the user is a teacher (has a Staff profile)
    """
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('login')
        
        try:
            staff = Staff.objects.get(user=request.user)
            # User has a staff profile, they are a teacher
            return view_func(request, *args, **kwargs)
        except Staff.DoesNotExist:
            # User doesn't have a staff profile, redirect to appropriate dashboard
            messages.error(request, 'Access denied. Teacher profile required.')
            return redirect('dashboard')  # or appropriate URL
    
    return _wrapped_view


def admin_required(view_func):
    """
    Decorator that prevents teachers from accessing admin-only functions
    """
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('login')
        
        # Check if user is superuser or admin
        if request.user.is_superuser or request.user.is_staff:
            return view_func(request, *args, **kwargs)
        
        # Check if user is a teacher (has Staff profile)
        try:
            staff = Staff.objects.get(user=request.user)
            # User is a teacher, redirect to teacher dashboard
            messages.warning(request, 'Access denied. This function is only available to administrators.')
            return redirect('teacher_dashboard')
        except Staff.DoesNotExist:
            # User is neither admin nor teacher
            messages.error(request, 'Access denied. Insufficient permissions.')
            return redirect('dashboard')
    
    return _wrapped_view


def teacher_read_only(view_func):
    """
    Decorator that allows teachers read-only access but prevents modifications
    """
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('login')
        
        # Allow GET requests (read-only)
        if request.method == 'GET':
            return view_func(request, *args, **kwargs)
        
        # For POST, PUT, DELETE requests, check if user is admin
        if request.user.is_superuser or request.user.is_staff:
            return view_func(request, *args, **kwargs)
        
        # Check if user is a teacher
        try:
            staff = Staff.objects.get(user=request.user)
            # Teacher trying to modify data
            messages.warning(request, 'Access denied. Teachers have read-only access to this information.')
            return redirect('teacher_dashboard')
        except Staff.DoesNotExist:
            messages.error(request, 'Access denied. Insufficient permissions.')
            return redirect('dashboard')
    
    return _wrapped_view
