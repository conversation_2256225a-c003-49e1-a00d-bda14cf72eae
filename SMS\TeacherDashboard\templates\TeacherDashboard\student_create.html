{% extends 'TeacherDashboard/base.html' %}
{% load humanize %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'teacher_dashboard' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Dashboard
        </a>
      </li>
      <li class="breadcrumb-item">
        <a href="{% url 'teacher_admissions' %}" class="text-decoration-none">
          <i class="fas fa-user-plus"></i> Admissions
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-plus"></i> New Student
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-user-plus{% endblock title-icon %}
{% block title %}{{ form_title }}{% endblock title %}
{% block subtitle %}Create new student admission{% endblock subtitle %}

{% block content %}
<div class="container-fluid">
  <!-- Teacher Access Notice -->
  <div class="row mb-3">
    <div class="col-12">
      <div class="alert alert-success border-left-success">
        <div class="d-flex align-items-center">
          <i class="fas fa-user-plus fa-2x text-success me-3"></i>
          <div>
            <h6 class="mb-1"><strong>Teacher Access - New Student Admission</strong></h6>
            <p class="mb-0">You can create new student admissions. Fill out the form below to admit a new student.</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0"><i class="fas fa-user-plus me-2"></i>{{ form_title }}</h5>
        </div>
        <div class="card-body">
          <form method="post" enctype="multipart/form-data">
            {% csrf_token %}
            
            <!-- Personal Information -->
            <div class="row mb-4">
              <div class="col-12">
                <h6 class="text-primary border-bottom pb-2 mb-3">
                  <i class="fas fa-user me-2"></i>Personal Information
                </h6>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="{{ form.fullname.id_for_label }}" class="form-label">
                    <strong>Full Name <span class="text-danger">*</span></strong>
                  </label>
                  {{ form.fullname }}
                  {% if form.fullname.errors %}
                    <div class="text-danger small">{{ form.fullname.errors }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="{{ form.gender.id_for_label }}" class="form-label">
                    <strong>Gender <span class="text-danger">*</span></strong>
                  </label>
                  {{ form.gender }}
                  {% if form.gender.errors %}
                    <div class="text-danger small">{{ form.gender.errors }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="{{ form.date_of_birth.id_for_label }}" class="form-label">
                    <strong>Date of Birth <span class="text-danger">*</span></strong>
                  </label>
                  {{ form.date_of_birth }}
                  {% if form.date_of_birth.errors %}
                    <div class="text-danger small">{{ form.date_of_birth.errors }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="{{ form.mobile_number.id_for_label }}" class="form-label">
                    <strong>Mobile Number</strong>
                  </label>
                  {{ form.mobile_number }}
                  {% if form.mobile_number.errors %}
                    <div class="text-danger small">{{ form.mobile_number.errors }}</div>
                  {% endif %}
                </div>
              </div>
            </div>

            <!-- Academic Information -->
            <div class="row mb-4">
              <div class="col-12">
                <h6 class="text-primary border-bottom pb-2 mb-3">
                  <i class="fas fa-graduation-cap me-2"></i>Academic Information
                </h6>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="{{ form.current_class.id_for_label }}" class="form-label">
                    <strong>Class <span class="text-danger">*</span></strong>
                  </label>
                  {{ form.current_class }}
                  {% if form.current_class.errors %}
                    <div class="text-danger small">{{ form.current_class.errors }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="{{ form.section.id_for_label }}" class="form-label">
                    <strong>Section</strong>
                  </label>
                  {{ form.section }}
                  {% if form.section.errors %}
                    <div class="text-danger small">{{ form.section.errors }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="{{ form.date_of_admission.id_for_label }}" class="form-label">
                    <strong>Date of Admission <span class="text-danger">*</span></strong>
                  </label>
                  {{ form.date_of_admission }}
                  {% if form.date_of_admission.errors %}
                    <div class="text-danger small">{{ form.date_of_admission.errors }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="{{ form.current_status.id_for_label }}" class="form-label">
                    <strong>Status</strong>
                  </label>
                  {{ form.current_status }}
                  {% if form.current_status.errors %}
                    <div class="text-danger small">{{ form.current_status.errors }}</div>
                  {% endif %}
                </div>
              </div>
            </div>

            <!-- Family Information -->
            <div class="row mb-4">
              <div class="col-12">
                <h6 class="text-primary border-bottom pb-2 mb-3">
                  <i class="fas fa-users me-2"></i>Family Information
                </h6>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="{{ form.Father_name.id_for_label }}" class="form-label">
                    <strong>Father's Name</strong>
                  </label>
                  {{ form.Father_name }}
                  {% if form.Father_name.errors %}
                    <div class="text-danger small">{{ form.Father_name.errors }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="{{ form.Father_mobile_number.id_for_label }}" class="form-label">
                    <strong>Father's Mobile</strong>
                  </label>
                  {{ form.Father_mobile_number }}
                  {% if form.Father_mobile_number.errors %}
                    <div class="text-danger small">{{ form.Father_mobile_number.errors }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="{{ form.Mother_name.id_for_label }}" class="form-label">
                    <strong>Mother's Name</strong>
                  </label>
                  {{ form.Mother_name }}
                  {% if form.Mother_name.errors %}
                    <div class="text-danger small">{{ form.Mother_name.errors }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="{{ form.Mother_mobile_number.id_for_label }}" class="form-label">
                    <strong>Mother's Mobile</strong>
                  </label>
                  {{ form.Mother_mobile_number }}
                  {% if form.Mother_mobile_number.errors %}
                    <div class="text-danger small">{{ form.Mother_mobile_number.errors }}</div>
                  {% endif %}
                </div>
              </div>
            </div>

            <!-- Additional Information -->
            <div class="row mb-4">
              <div class="col-12">
                <h6 class="text-primary border-bottom pb-2 mb-3">
                  <i class="fas fa-info-circle me-2"></i>Additional Information
                </h6>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="{{ form.aadhar.id_for_label }}" class="form-label">
                    <strong>Aadhar Number</strong>
                  </label>
                  {{ form.aadhar }}
                  {% if form.aadhar.errors %}
                    <div class="text-danger small">{{ form.aadhar.errors }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="{{ form.passport_photo.id_for_label }}" class="form-label">
                    <strong>Passport Photo</strong>
                  </label>
                  {{ form.passport_photo }}
                  {% if form.passport_photo.errors %}
                    <div class="text-danger small">{{ form.passport_photo.errors }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="col-12">
                <div class="mb-3">
                  <label for="{{ form.address.id_for_label }}" class="form-label">
                    <strong>Address</strong>
                  </label>
                  {{ form.address }}
                  {% if form.address.errors %}
                    <div class="text-danger small">{{ form.address.errors }}</div>
                  {% endif %}
                </div>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="row">
              <div class="col-12">
                <div class="d-flex justify-content-between">
                  <a href="{% url 'teacher_admissions' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Admissions
                  </a>
                  <div>
                    <button type="reset" class="btn btn-outline-warning me-2">
                      <i class="fas fa-undo me-2"></i>Reset Form
                    </button>
                    <button type="submit" class="btn btn-success">
                      <i class="fas fa-save me-2"></i>Admit Student
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Function to load sections based on selected class
function loadSections(classId) {
    if (classId) {
        fetch(`/student/api/class/${classId}/sections/`)
            .then(response => response.json())
            .then(data => {
                const sectionSelect = document.getElementById('id_section');
                sectionSelect.innerHTML = '<option value="">Select Section</option>';
                data.forEach(section => {
                    sectionSelect.innerHTML += `<option value="${section.name}">${section.name}</option>`;
                });
            })
            .catch(error => console.error('Error loading sections:', error));
    }
}

// Set today's date as default for admission date
document.addEventListener('DOMContentLoaded', function() {
    const admissionDateField = document.getElementById('id_date_of_admission');
    if (admissionDateField && !admissionDateField.value) {
        const today = new Date().toISOString().split('T')[0];
        admissionDateField.value = today;
    }
});
</script>
{% endblock content %}
