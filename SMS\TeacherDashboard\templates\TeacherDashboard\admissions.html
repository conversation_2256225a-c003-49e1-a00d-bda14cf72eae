{% extends 'TeacherDashboard/base.html' %}
{% load humanize %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'teacher_dashboard' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Dashboard
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-user-plus"></i> Admissions
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-user-plus{% endblock title-icon %}
{% block title %}Admissions{% endblock title %}
{% block subtitle %}View admission information and statistics{% endblock subtitle %}

{% block content %}
<div class="container-fluid">
  <!-- Teacher Access Notice -->
  <div class="row mb-3">
    <div class="col-12">
      <div class="alert alert-info border-left-info">
        <div class="d-flex align-items-center">
          <i class="fas fa-eye fa-2x text-info me-3"></i>
          <div>
            <h6 class="mb-1"><strong>Teacher View - Admissions Information</strong></h6>
            <p class="mb-0">You can view admission statistics and recent admissions. Only administrators can create new admissions.</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Statistics Cards -->
  <div class="row mb-4">
    <div class="col-md-4">
      <div class="card bg-primary text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4 class="mb-0">{{ this_year_admissions }}</h4>
              <p class="mb-0">This Year Admissions</p>
            </div>
            <div class="align-self-center">
              <i class="fas fa-calendar-alt fa-2x"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="card bg-success text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4 class="mb-0">{{ recent_admissions }}</h4>
              <p class="mb-0">Last 30 Days</p>
            </div>
            <div class="align-self-center">
              <i class="fas fa-user-check fa-2x"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="card bg-warning text-dark">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4 class="mb-0">View Only</h4>
              <p class="mb-0">Teacher Access</p>
            </div>
            <div class="align-self-center">
              <i class="fas fa-lock fa-2x"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Recent Admissions -->
  <div class="row">
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-user-plus me-2"></i>Recent Admissions</h5>
            <span class="badge bg-light text-dark">
              <i class="fas fa-info-circle me-1"></i>View Only Access
            </span>
          </div>
        </div>
        <div class="card-body">
          {% if recent_students %}
            <div class="table-responsive">
              <table class="table table-striped table-hover" id="admissionsTable">
                <thead class="table-dark">
                  <tr>
                    <th>#</th>
                    <th>Registration No.</th>
                    <th>Student Name</th>
                    <th>Class</th>
                    <th>Section</th>
                    <th>Admission Date</th>
                    <th>Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for student in recent_students %}
                  <tr>
                    <td>{{ forloop.counter }}</td>
                    <td>{{ student.registration_number|default:"N/A" }}</td>
                    <td>
                      <strong>{{ student.fullname }}</strong>
                    </td>
                    <td>{{ student.current_class|default:"N/A" }}</td>
                    <td>{{ student.section|default:"N/A" }}</td>
                    <td>{{ student.date_of_admission|date:"d M Y"|default:"N/A" }}</td>
                    <td>
                      <span class="badge {% if student.current_status == 'active' %}bg-success{% else %}bg-danger{% endif %}">
                        {{ student.current_status|title }}
                      </span>
                    </td>
                    <td>
                      <a href="{% url 'teacher_student_detail' student.pk %}" class="btn btn-sm btn-info" title="View Student Details">
                        <i class="fas fa-eye"></i> View
                      </a>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          {% else %}
            <div class="text-center py-5">
              <i class="fas fa-user-plus fa-3x text-muted mb-3"></i>
              <h5 class="text-muted">No Recent Admissions</h5>
              <p class="text-muted">No recent admissions found.</p>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>

  <!-- Information Panel -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card border-info">
        <div class="card-header bg-info text-white">
          <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Information for Teachers</h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <h6><i class="fas fa-check-circle text-success me-2"></i>What you can do:</h6>
              <ul class="list-unstyled">
                <li><i class="fas fa-eye text-info me-2"></i>View admission statistics</li>
                <li><i class="fas fa-eye text-info me-2"></i>View recently admitted students</li>
                <li><i class="fas fa-eye text-info me-2"></i>Access student details</li>
                <li><i class="fas fa-calendar-check text-info me-2"></i>Mark attendance for students</li>
              </ul>
            </div>
            <div class="col-md-6">
              <h6><i class="fas fa-times-circle text-danger me-2"></i>Admin-only functions:</h6>
              <ul class="list-unstyled">
                <li><i class="fas fa-user-plus text-muted me-2"></i>Create new admissions</li>
                <li><i class="fas fa-edit text-muted me-2"></i>Edit student information</li>
                <li><i class="fas fa-trash text-muted me-2"></i>Delete student records</li>
                <li><i class="fas fa-upload text-muted me-2"></i>Bulk upload students</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
$(document).ready(function() {
    $('#admissionsTable').DataTable({
        "pageLength": 25,
        "order": [[ 5, "desc" ]], // Sort by admission date (newest first)
        "columnDefs": [
            { "orderable": false, "targets": [7] } // Disable sorting on Actions column
        ]
    });
});
</script>
{% endblock content %}
