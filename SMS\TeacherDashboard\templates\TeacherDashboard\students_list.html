{% extends 'TeacherDashboard/base.html' %}
{% load humanize %}

{% block breadcrumb-left %}
<div class="breadcrumb-container">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb breadcrumb-chevron">
      <li class="breadcrumb-item">
        <a href="{% url 'teacher_dashboard' %}" class="text-decoration-none fw-bold">
          <i class="fas fa-home"></i> Dashboard
        </a>
      </li>
      <li class="breadcrumb-item active" aria-current="page">
        <i class="fas fa-user-graduate"></i> My Students
      </li>
    </ol>
  </nav>
</div>
{% endblock breadcrumb-left %}

{% block title-icon %}fas fa-user-graduate{% endblock title-icon %}
{% block title %}My Students{% endblock title %}
{% block subtitle %}View and manage your assigned students{% endblock subtitle %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <!-- Summary Cards -->
      <div class="row mb-4">
        <div class="col-md-3">
          <div class="card bg-primary text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h4 class="mb-0">{{ total_students }}</h4>
                  <p class="mb-0">Total Students</p>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-users fa-2x"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card bg-success text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h4 class="mb-0">{{ students|length }}</h4>
                  <p class="mb-0">Active Students</p>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-user-check fa-2x"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card bg-info text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h4 class="mb-0">View Only</h4>
                  <p class="mb-0">Teacher Access</p>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-eye fa-2x"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card bg-warning text-dark">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h4 class="mb-0">Read Only</h4>
                  <p class="mb-0">No Edit Access</p>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-lock fa-2x"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-user-graduate me-2"></i>My Students (View Only)</h5>
            <div class="badge bg-light text-dark">
              <i class="fas fa-info-circle me-1"></i>Teachers can only view student information
            </div>
          </div>
        </div>
        <div class="card-body">
          {% if students %}
            <div class="table-responsive">
              <table class="table table-striped table-hover" id="studentsTable">
                <thead class="table-dark">
                  <tr>
                    <th>#</th>
                    <th>Registration No.</th>
                    <th>Name</th>
                    <th>Class</th>
                    <th>Section</th>
                    <th>Gender</th>
                    <th>Mobile</th>
                    <th>Status</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {% for student in students %}
                  <tr>
                    <td>{{ forloop.counter }}</td>
                    <td>{{ student.registration_number|default:"N/A" }}</td>
                    <td>
                      <strong>{{ student.fullname }}</strong>
                    </td>
                    <td>{{ student.current_class|default:"N/A" }}</td>
                    <td>{{ student.section|default:"N/A" }}</td>
                    <td>{{ student.get_gender_display|default:"N/A" }}</td>
                    <td>{{ student.mobile_number|default:"N/A" }}</td>
                    <td>
                      <span class="badge {% if student.current_status == 'active' %}bg-success{% else %}bg-danger{% endif %}">
                        {{ student.current_status|title }}
                      </span>
                    </td>
                    <td>
                      <a href="{% url 'teacher_student_detail' student.pk %}" class="btn btn-sm btn-info" title="View Student Details">
                        <i class="fas fa-eye"></i> View
                      </a>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          {% else %}
            <div class="text-center py-5">
              <i class="fas fa-user-graduate fa-3x text-muted mb-3"></i>
              <h5 class="text-muted">No Students Found</h5>
              <p class="text-muted">No students are currently available to view.</p>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>

<script>
$(document).ready(function() {
    $('#studentsTable').DataTable({
        "pageLength": 25,
        "order": [[ 2, "asc" ]], // Sort by name
        "columnDefs": [
            { "orderable": false, "targets": [8] } // Disable sorting on Actions column
        ]
    });
});
</script>
{% endblock content %}
